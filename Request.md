I want to implement a comprehensive GIS/mapping solution for bridge engineering analysis using the three moment equation method. This should be built as a Phoenix LiveView application with Leaflet.js integration. The system needs to handle route drawing, bridge intersection detection, structural analysis calculations, and dynamic data collection.

**Core Functionality Requirements:**
1. **Interactive Map Interface (Leaflet.js):**
   - Implement polyline drawing tools allowing users to create routes by clicking points on the map
   - Display existing bridge locations as interactive markers with visual indicators (icons/symbols)
   - Provide real-time geometric intersection detection between drawn routes and bridge locations
   - Enable pan, zoom, and layer controls for map navigation

2. **Bridge Detection & Analysis System:**
   - Automatically detect when drawn routes intersect with bridge marker boundaries
   - Trigger three moment equation calculations for all intersected bridges
   - Validate that required structural and vehicle data exists before enabling calculations
   - Display calculation results with visual feedback (color-coded markers: green=pass, red=fail)

3. **Dynamic Data Collection:**
   - Generate context-aware forms for missing bridge parameters
   - Provide vehicle specification input forms when needed
   - Implement real-time form validation with clear error messaging
   - Allow editing of existing bridge/vehicle data through modal dialogs

**Technical Implementation Specifications:**

1. **Frontend Architecture (Leaflet.js + Phoenix LiveView):**
   - Use Leaflet.draw plugin for route creation functionality
   - Implement JavaScript hooks for bidirectional communication with LiveView
   - Create custom bridge marker components with click handlers
   - Handle real-time map updates when calculation results change
   - Implement proper error handling for map interactions

2. **Backend Architecture (Phoenix LiveView + Elixir):**
   - Create Ecto schemas for bridges, vehicles, routes, and analysis results
   - Implement three moment equation calculations using proper structural engineering formulas
   - Build LiveView components for dynamic form generation
   - Handle real-time updates using Phoenix PubSub for multi-user scenarios
   - Implement proper data validation and error handling

3. **Data Models & Requirements:**
   - **Bridge Data:** span lengths (m), support conditions (fixed/pinned/roller), material properties (E, I), load ratings (kN), coordinates (lat/lng)
   - **Vehicle Data:** gross vehicle weight (kN), individual axle loads (kN), wheelbase dimensions (m), vehicle classification type
   - **Route Data:** coordinate arrays (lat/lng pairs), optional elevation profile data
   - **Analysis Results:** moment values, stress calculations, pass/fail status, safety factors

**Detailed User Workflow:**
1. User opens the map interface and sees existing bridge markers
2. User activates route drawing mode and clicks points to create a polyline route
3. System automatically detects intersections with bridge locations in real-time
4. "Analyze Route" button appears but remains disabled until data validation passes
5. System checks each intersected bridge for complete structural data
6. Missing bridge data is highlighted on map with visual indicators (red markers/badges)
7. User clicks on bridges with missing data to open data entry forms
8. Forms are pre-populated with any existing data and show required vs. optional fields
9. Once all bridge data is complete, the "Analyze Route" button becomes enabled
10. User clicks "Analyze Route" button
11. If vehicle data is missing, a vehicle specification form appears as a modal
12. User completes vehicle data entry with validation feedback
13. System performs three moment equation calculations for all intersected bridges
14. Map updates with color-coded results: green markers for bridges that pass analysis, red for failures
15. User can click on analyzed bridges to view detailed calculation results and safety factors

**Technical Integration Requirements:**
- Implement Phoenix.LiveView.JS for seamless JavaScript interop
- Use Phoenix channels for real-time map updates
- Create reusable LiveView components for forms and modals
- Implement proper error boundaries and loading states
- Ensure responsive design for mobile and desktop use
- Add proper accessibility features (ARIA labels, keyboard navigation)

**Engineering Calculation Specifications:**
- Implement the three moment equation method for continuous beam analysis
- Handle various support conditions (simple, fixed, cantilever)
- Calculate maximum moments and compare against bridge capacity
- Apply appropriate safety factors and load combinations
- Generate detailed calculation reports with intermediate steps

Please implement this system with a focus on user experience, proper error handling, and maintainable code structure. The solution should demonstrate best practices for Phoenix LiveView applications with complex JavaScript integration.