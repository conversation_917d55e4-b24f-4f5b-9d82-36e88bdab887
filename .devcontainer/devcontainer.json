{
  "name": "Starlight Phoenix Development",
  "image": "mcr.microsoft.com/devcontainers/elixir:1.14-otp-25",

  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "extensions": [
        // Elixir language support
        "jakebecker.elixir-ls",
        "phoenixframework.phoenix",

        // Database support
        "ms-ossdata.vscode-postgresql",

        // Web development
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-json",
        "esbenp.prettier-vscode",

        // Git and development workflow
        "eamodio.gitlens",
        "ms-vscode.vscode-git-graph",

        // AI assistance
        "augmentcode.augment",

        // General productivity
        "ms-vscode.vscode-todo-highlight",
        "streetsidesoftware.code-spell-checker"
      ],
      "settings": {
        // Elixir configuration
        "elixirLS.dialyzerEnabled": true,
        "elixirLS.fetchDeps": false,
        "elixirLS.suggestSpecs": true,

        // Editor configuration
        "editor.formatOnSave": true,
        "editor.insertSpaces": true,
        "editor.tabSize": 2,

        // File associations
        "files.associations": {
          "*.heex": "phoenix-heex",
          "*.ex": "elixir",
          "*.exs": "elixir"
        },

        // Terminal configuration
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "/bin/bash"
          }
        }
      }
    }
  },

  // Features to add to the dev container
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "18"
    },
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers/features/postgres:1": {
      "version": "15"
    }
  },

  // Port forwarding for development
  "forwardPorts": [
    4000,  // Phoenix development server
    4002,  // Phoenix test server (if needed)
    5432   // PostgreSQL database
  ],
  "portsAttributes": {
    "4000": {
      "label": "Phoenix Server",
      "onAutoForward": "notify"
    },
    "5432": {
      "label": "PostgreSQL",
      "onAutoForward": "silent"
    }
  },

  // Environment variables
  "containerEnv": {
    "MIX_ENV": "dev"
  },

  // Commands to run after container creation
  "postCreateCommand": "bash .devcontainer/setup.sh",

  // Keep container running
  "shutdownAction": "stopContainer",

  // Run as non-root user for security
  "remoteUser": "vscode",

  // Container user configuration
  "updateRemoteUserUID": true
}
