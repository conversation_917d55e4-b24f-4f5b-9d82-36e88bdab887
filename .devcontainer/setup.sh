#!/bin/bash

# Starlight Phoenix Development Environment Setup Script
# This script runs after the devcontainer is created to set up the development environment

set -e

echo "🚀 Setting up Starlight Phoenix development environment..."

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update -qq

# Install additional system dependencies
echo "🔧 Installing system dependencies..."
sudo apt-get install -y -qq \
  inotify-tools \
  postgresql-client \
  curl \
  wget \
  git \
  build-essential

# Configure Git (if not already configured)
echo "🔧 Configuring Git..."
if [ -z "$(git config --global user.name)" ]; then
  echo "⚠️  Git user.name not set. Please configure with: git config --global user.name 'Your Name'"
fi
if [ -z "$(git config --global user.email)" ]; then
  echo "⚠️  Git user.email not set. Please configure with: git config --global user.email '<EMAIL>'"
fi

# Start PostgreSQL service
echo "🗄️  Starting PostgreSQL service..."
sudo service postgresql start

# Wait for PostgreSQL to be ready
echo "🗄️  Waiting for PostgreSQL to be ready..."
until pg_isready -h localhost -p 5432 -U postgres; do
  echo "Waiting for PostgreSQL..."
  sleep 2
done

echo "✅ PostgreSQL is ready!"

# Create databases
echo "🗄️  Creating databases..."
sudo -u postgres createdb starlight_dev || echo "Database starlight_dev might already exist"
sudo -u postgres createdb starlight_test || echo "Database starlight_test might already exist"

# Install Hex and Rebar (Elixir package managers)
echo "📦 Installing Hex and Rebar..."
mix local.hex --force
mix local.rebar --force

# Install Phoenix archive
echo "🔥 Installing Phoenix archive..."
mix archive.install hex phx_new --force

# Install project dependencies
echo "📦 Installing project dependencies..."
mix deps.get

# Compile dependencies
echo "🔨 Compiling dependencies..."
mix deps.compile

# Set up the database
echo "🗄️  Setting up database..."
mix ecto.create || echo "Database might already exist, continuing..."
mix ecto.migrate

# Install and setup assets
echo "🎨 Setting up assets..."
mix assets.setup

# Build assets
echo "🏗️  Building assets..."
mix assets.build

# Compile the project
echo "🔨 Compiling project..."
mix compile

# Run tests to verify everything is working
echo "🧪 Running tests to verify setup..."
mix test --max-failures=5 || echo "⚠️  Some tests failed, but setup continues..."

# Set up shell environment
echo "🐚 Setting up shell environment..."
echo 'export MIX_ENV=dev' >> ~/.bashrc
echo 'export PATH="$PATH:/workspace/_build/dev/lib/starlight/priv"' >> ~/.bashrc

# Create useful aliases
echo "🔗 Setting up useful aliases..."
cat >> ~/.bashrc << 'EOF'

# Phoenix/Elixir aliases
alias mps='mix phx.server'
alias mt='mix test'
alias mc='mix compile'
alias mf='mix format'
alias mdr='mix deps.get && mix deps.compile'
alias mec='mix ecto.create'
alias mem='mix ecto.migrate'
alias mer='mix ecto.reset'
alias iex='iex -S mix'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline'

EOF

echo ""
echo "🎉 Setup complete! Your Starlight Phoenix development environment is ready."
echo ""
echo "📋 Quick start commands:"
echo "  mix phx.server    - Start the Phoenix development server"
echo "  mix test          - Run the test suite"
echo "  mix ecto.reset    - Reset the database"
echo "  iex -S mix        - Start interactive Elixir shell with project loaded"
echo ""
echo "🌐 The Phoenix server will be available at http://localhost:4000"
echo "🗄️  PostgreSQL is available at localhost:5432 (user: postgres, password: postgres)"
echo ""
echo "💡 Tip: Use 'mps' alias to quickly start the Phoenix server!"
