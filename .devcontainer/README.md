# Starlight Phoenix Development Container

This directory contains the GitHub Codespaces / VS Code Dev Container configuration for the Starlight Phoenix application.

## Overview

The devcontainer provides a complete development environment with:

- **Elixir 1.14** with OTP 25
- **Phoenix Framework** with LiveView support
- **PostgreSQL 15** database
- **Node.js 18** for asset compilation
- **Essential VS Code extensions** for Elixir/Phoenix development
- **Pre-configured database** with development and test databases

## Quick Start

1. **Open in GitHub Codespaces**: Click the "Code" button in GitHub and select "Create codespace on main"
2. **Open in VS Code**: Use the "Dev Containers: Reopen in Container" command
3. **Wait for setup**: The container will automatically install dependencies and set up the database
4. **Start developing**: Run `mix phx.server` or use the `mps` alias

## What's Included

### Base Environment
- **Base Image**: `mcr.microsoft.com/devcontainers/elixir:1.14-otp-25`
- **PostgreSQL**: Version 15 with automatic database creation
- **Node.js**: Version 18 for asset compilation (esbuild, tailwind)
- **Git**: Pre-configured with GitHub CLI

### VS Code Extensions
- **Elixir LS**: Language server for Elixir with IntelliSense, debugging, and formatting
- **Phoenix**: Phoenix framework support with LiveView syntax highlighting
- **PostgreSQL**: Database management and query tools
- **Tailwind CSS**: CSS framework support with IntelliSense
- **Augment Code**: AI-powered coding assistance
- **GitLens**: Enhanced Git capabilities

### Development Tools
- **Mix**: Elixir build tool and package manager
- **Phoenix**: Web framework with LiveView
- **Ecto**: Database wrapper and query generator
- **ExUnit**: Testing framework
- **Dialyzer**: Static analysis tool (enabled in ElixirLS)

## Port Forwarding

The following ports are automatically forwarded:

- **4000**: Phoenix development server (main application)
- **4002**: Phoenix test server (if needed)
- **5432**: PostgreSQL database

## Environment Variables

- `MIX_ENV=dev`: Set to development environment by default

## Useful Commands

The setup script creates several helpful aliases:

### Phoenix/Elixir
- `mps` → `mix phx.server` - Start Phoenix server
- `mt` → `mix test` - Run tests
- `mc` → `mix compile` - Compile project
- `mf` → `mix format` - Format code
- `mdr` → `mix deps.get && mix deps.compile` - Update dependencies
- `iex` → `iex -S mix` - Interactive Elixir with project loaded

### Database
- `mec` → `mix ecto.create` - Create database
- `mem` → `mix ecto.migrate` - Run migrations
- `mer` → `mix ecto.reset` - Reset database

### Git
- `gs` → `git status`
- `ga` → `git add`
- `gc` → `git commit`
- `gp` → `git push`
- `gl` → `git log --oneline`

## Database Configuration

The PostgreSQL database is configured with:
- **Host**: localhost
- **Port**: 5432
- **User**: postgres
- **Password**: postgres (for development only)
- **Databases**: 
  - `starlight_dev` (development)
  - `starlight_test` (testing)

## Troubleshooting

### Container Won't Start
- Check that Docker is running
- Ensure you have sufficient disk space
- Try rebuilding the container: "Dev Containers: Rebuild Container"

### Database Connection Issues
- Verify PostgreSQL is running: `sudo service postgresql status`
- Check database exists: `sudo -u postgres psql -l`
- Restart PostgreSQL: `sudo service postgresql restart`

### Asset Compilation Issues
- Ensure Node.js is available: `node --version`
- Reinstall assets: `mix assets.setup`
- Rebuild assets: `mix assets.build`

### ElixirLS Issues
- Restart the language server: "Elixir LS: Restart"
- Clear build artifacts: `mix clean`
- Recompile dependencies: `mix deps.compile --force`

## Customization

### Adding Extensions
Edit `.devcontainer/devcontainer.json` and add extension IDs to the `extensions` array.

### Modifying Setup
Edit `.devcontainer/setup.sh` to add additional setup steps.

### Environment Variables
Add environment variables to the `containerEnv` section in `devcontainer.json`.

## Performance Optimization

The devcontainer is optimized for fast startup:
- Uses official Microsoft devcontainer base images
- Leverages devcontainer features for common tools
- Minimal custom installation in setup script
- Cached workspace mounting for file system performance

## Security

- Runs as non-root user (`vscode`)
- Uses official, maintained base images
- Database credentials are for development only
- No production secrets in configuration
